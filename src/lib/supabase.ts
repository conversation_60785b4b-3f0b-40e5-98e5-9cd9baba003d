import { createClient } from '@supabase/supabase-js'

// Client-side Supabase client (with anon key)
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Server-side Supabase client (with service role key for admin operations)
// This should only be used on the server side
export const createSupabaseAdmin = () => {
  if (typeof window !== 'undefined') {
    throw new Error('supabaseAdmin should only be used on the server side')
  }

  return createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

// Storage bucket name
export const STORAGE_BUCKET = 'files'

// File upload configuration
export const FILE_UPLOAD_CONFIG = {
  maxFileSize: 50 * 1024 * 1024, // 50MB
  allowedMimeTypes: [
    // Images
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    
    // Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    
    // Spreadsheets
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
    
    // Text files
    'text/plain',
    'text/markdown',
    'application/json',
    
    // Archives
    'application/zip',
    'application/x-rar-compressed',
  ],
  allowedExtensions: [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
    '.pdf', '.doc', '.docx',
    '.xls', '.xlsx', '.csv',
    '.txt', '.md', '.json',
    '.zip', '.rar'
  ]
}

// Helper function to validate file
export function validateFile(file: File) {
  const errors: string[] = []
  
  // Check file size
  if (file.size > FILE_UPLOAD_CONFIG.maxFileSize) {
    errors.push(`File size must be less than ${FILE_UPLOAD_CONFIG.maxFileSize / (1024 * 1024)}MB`)
  }
  
  // Check MIME type
  if (!FILE_UPLOAD_CONFIG.allowedMimeTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`)
  }
  
  // Check file extension
  const extension = '.' + file.name.split('.').pop()?.toLowerCase()
  if (!FILE_UPLOAD_CONFIG.allowedExtensions.includes(extension)) {
    errors.push(`File extension ${extension} is not allowed`)
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Helper function to generate storage path
export function generateStoragePath(userId: string, filename: string): string {
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)
  const extension = filename.split('.').pop()
  const sanitizedName = filename.replace(/[^a-zA-Z0-9.-]/g, '_')
  
  return `${userId}/${timestamp}_${randomString}_${sanitizedName}`
}

// Helper function to get file type category
export function getFileTypeCategory(mimeType: string): string {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType === 'application/pdf') return 'pdf'
  if (mimeType.includes('spreadsheet') || mimeType.includes('excel') || mimeType === 'text/csv') return 'spreadsheet'
  if (mimeType.includes('document') || mimeType.includes('word')) return 'document'
  if (mimeType.startsWith('text/')) return 'text'
  if (mimeType.includes('zip') || mimeType.includes('rar')) return 'archive'
  return 'other'
}
