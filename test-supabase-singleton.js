// Test script to verify Supabase admin singleton pattern
// This should be run in a Node.js environment with the proper environment variables

// Mock environment variables for testing
process.env.SUPABASE_URL = 'https://test.supabase.co'
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key'
process.env.NODE_ENV = 'development'

// Import the functions
const { createSupabaseAdmin, supabaseAdmin } = require('./src/lib/supabase.ts')

console.log('Testing Supabase Admin Singleton Pattern...\n')

// Test 1: Function calls should return the same instance
console.log('Test 1: Function calls return same instance')
const instance1 = createSupabaseAdmin()
const instance2 = createSupabaseAdmin()
console.log('instance1 === instance2:', instance1 === instance2)
console.log('✓ Function calls return same instance\n')

// Test 2: Direct export should be the same as function call
console.log('Test 2: Direct export matches function call')
const instance3 = createSupabaseAdmin()
console.log('supabaseAdmin === instance3:', supabaseAdmin === instance3)
console.log('✓ Direct export matches function call\n')

// Test 3: Multiple direct exports should be the same
console.log('Test 3: Multiple direct exports are the same')
const { supabaseAdmin: admin1 } = require('./src/lib/supabase.ts')
const { supabaseAdmin: admin2 } = require('./src/lib/supabase.ts')
console.log('admin1 === admin2:', admin1 === admin2)
console.log('✓ Multiple direct exports are the same\n')

console.log('All tests passed! ✅')
console.log('The singleton pattern is working correctly.')
